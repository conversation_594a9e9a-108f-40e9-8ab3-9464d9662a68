# Enhance Button Implementation Plan

## Current Status: BROKEN - Need to Reset and Simplify

### Problem Statement

The enhance button logic has become overly complex and doesn't meet the basic requirements. We need simple validation and clear button behavior.

## Requirements (User Specified)

### Validation - ALL Required Fields Need 50+ Characters:

1. **Title** - Backend identifier field
2. **Enhanced English Title** - AI-optimized title
3. **Enhanced English Summary** - AI-optimized summary
4. **Enhanced English Content** - AI-optimized content

### Button Behavior:

- **Validation Fails** → Button disabled with clear message
- **Validation Passes** → Show "Enhance Content" (blue button)
- **After AI Enhancement** → Show "Re-enhance Content" (purple button)
- **Translation Available** → Show "Translate to German" (like generated articles)

### Current Issues:

- ❌ Only Enhanced English Content being validated
- ❌ Showing "Re-enhance" incorrectly
- ❌ Translation button missing
- ❌ Over-engineered detection logic
- ❌ Not respecting PayloadCMS native patterns

## Implementation Plan

### Step 1: Reset Button Logic (Simple Validation)

```typescript
// Simple field validation - no complex detection
const validateRequiredFields = () => {
  const title = data?.title || '';
  const enhancedTitle =
    data?.['englishTab.enhancedTitle'] ||
    fields?.['englishTab.enhancedTitle']?.value ||
    '';
  const enhancedSummary =
    data?.['englishTab.enhancedSummary'] ||
    fields?.['englishTab.enhancedSummary']?.value ||
    '';
  const enhancedContent =
    data?.['englishTab.enhancedContent'] ||
    fields?.['englishTab.enhancedContent']?.value;

  // Convert Lexical content to text for length check (only for content field)
  let contentText = '';
  if (enhancedContent && typeof enhancedContent === 'object') {
    contentText = lexicalToText(enhancedContent);
  }

  return {
    title: title.length >= 50,
    enhancedTitle: enhancedTitle.length >= 50,
    enhancedSummary: enhancedSummary.length >= 50,
    enhancedContent: contentText.length >= 50,
  };
};
```

### Step 2: Simple Button States

```typescript
const validation = validateRequiredFields();
const allFieldsValid = Object.values(validation).every(v => v);

// Simple detection: Has quality scores = already enhanced by AI
const isEnhanced = !!(
  data?.['englishTab.qualityScore'] || enhancementJustCompleted
);

const canEnhance =
  articleType === 'curated' &&
  workflowStage === 'curated-draft' &&
  allFieldsValid &&
  !isEnhancing &&
  !isTranslating;

const canTranslate = isEnhanced; // Can translate after enhancement
```

### Step 3: Clear Button Text & Messages

```typescript
const getEnhanceButtonText = () => {
  if (isEnhancing) return 'Enhancing Content...';
  if (enhancementJustCompleted) return 'Enhancement Complete! Refreshing...';
  if (isEnhanced) return 'Re-enhance Content';
  return 'Enhance Content';
};

const getValidationMessage = () => {
  if (!validation.title) return 'Title needs at least 50 characters';
  if (!validation.enhancedTitle)
    return 'Enhanced English Title needs at least 50 characters';
  if (!validation.enhancedSummary)
    return 'Enhanced English Summary needs at least 50 characters';
  if (!validation.enhancedContent)
    return 'Enhanced English Content needs at least 50 characters';
  return null;
};
```

### Step 4: Both Buttons Available (Like Generated Articles)

```typescript
// Show enhance button for curated articles in draft stage
const showEnhanceButton =
  articleType === 'curated' && workflowStage === 'curated-draft';

// Show translate button when content is enhanced (same as generated articles)
const showTranslateButton =
  isEnhanced &&
  [
    'curated-draft',
    'candidate-article',
    'translated',
    'ready-for-review',
    'published',
  ].includes(workflowStage);
```

## Impact Analysis

### ✅ Will Not Break:

- **Generated Articles** - Different articleType, no changes to that flow
- **PayloadCMS Field State** - Using standard field access patterns
- **Translation Workflow** - Reusing existing translation logic
- **Enhancement API** - API remains unchanged

### ✅ Will Fix:

- Simple validation for all required fields
- Clear button states and messages
- Translation availability after enhancement
- Native PayloadCMS patterns
- Consistent with generated articles workflow

## Testing Plan

### Test Case 1: Empty Fields

- All fields empty → Button disabled with "Title needs at least 50 characters"

### Test Case 2: Partial Fields

- Title: 50+ chars, others empty → "Enhanced English Title needs at least 50 characters"

### Test Case 3: All Fields Valid

- All fields 50+ chars → "Enhance Content" enabled

### Test Case 4: After Enhancement

- Click enhance → API processes → "Re-enhance Content" + "Translate to German" available

### Test Case 5: Generated Articles (No Impact)

- Generated articles continue to work exactly as before
- No changes to their workflow

## Implementation Steps

1. **Reset validation logic** - Remove complex detection, use simple field length checks
2. **Fix button text logic** - Use quality scores to detect enhanced content
3. **Add translation button** - Show after enhancement like generated articles
4. **Test all scenarios** - Ensure no regression in generated articles
5. **Clean up debug code** - Remove debugging once working

## Key Principles

- **Keep It Simple** - No complex content analysis or custom validation
- **PayloadCMS Native** - Use standard field access patterns
- **Consistent Behavior** - Match generated articles workflow where possible
- **Clear User Feedback** - Specific validation messages for each field
- **No Breaking Changes** - Generated articles unaffected
