/**
 * Article Document Controls Component
 *
 * Provides translation functionality for candidate articles with proper UI updates.
 *
 * Key Features:
 * - Translates enhanced English content to German using OpenAI
 * - Updates form fields programmatically using PayloadCMS patterns
 * - Forces page refresh to ensure UI reflects changes immediately
 * - Provides visual feedback during translation process
 * - <PERSON>les re-translation scenarios
 *
 * Solution for UI Update Issue:
 * - Uses dispatchFields() to update form state
 * - Implements router.refresh() to force page re-render
 * - Provides immediate visual feedback with button state changes
 * - Includes fallback option for window.location.reload() if needed
 *
 * <AUTHOR> Blick Development Team
 * @updated 2025-01-16 - Fixed UI refresh issue after translation
 */
'use client';

import React, { useState, useCallback } from 'react';
import { useDocumentInfo, useAllFormFields } from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import { lexicalToText } from '../../../lib/utils/lexical-text';

// Temporary toast implementation - will be replaced with proper PayloadCMS notifications
interface ToastOptions {
  description?: string;
  duration?: number;
}

const useToast = () => {
  return {
    toast: {
      error: (message: string, options?: ToastOptions) =>
        console.error('Toast Error:', message, options),
      success: (message: string, options?: ToastOptions) =>
        console.log('Toast Success:', message, options),
    },
  };
};

// Helper to reduce fields to values - simplified for our use case
const reduceFieldsToValues = (fields: any) => {
  const values: any = {};
  Object.keys(fields).forEach(key => {
    if (
      fields[key] &&
      typeof fields[key] === 'object' &&
      'value' in fields[key]
    ) {
      values[key] = fields[key].value;
    }
  });
  return values;
};

export const ArticleDocumentControls = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  const { toast } = useToast();
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);

  // Enhancement state management - follows same pattern as translation
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementJustCompleted, setEnhancementJustCompleted] =
    useState(false);

  // Convert fields to data using PayloadCMS helper
  const data = reduceFieldsToValues(fields);
  const workflowStage = data?.workflowStage;
  const articleType = data?.articleType;
  const hasGermanTranslation =
    data?.germanTab?.germanTitle && data?.germanTab?.germanContent;

  // Simple field validation for ALL required fields (50+ characters each)
  // Using proper PayloadCMS nested object access patterns
  const validateRequiredFields = () => {
    const title = data?.title || '';
    const enhancedTitle = data?.englishTab?.enhancedTitle || '';
    const enhancedSummary = data?.englishTab?.enhancedSummary || '';
    const enhancedContent = data?.englishTab?.enhancedContent;

    // Convert Lexical content to text for length check (only for content field)
    let contentText = '';
    if (enhancedContent && typeof enhancedContent === 'object') {
      contentText = lexicalToText(enhancedContent);
    }

    return {
      title: title.length >= 50,
      enhancedTitle: enhancedTitle.length >= 50,
      enhancedSummary: enhancedSummary.length >= 50,
      enhancedContent: contentText.length >= 50,
    };
  };

  const validation = validateRequiredFields();
  const allFieldsValid = Object.values(validation).every(v => v);

  // Simple detection: Has been enhanced flag = already enhanced by AI (same pattern as translation)
  const isEnhanced = !!(data?.hasBeenEnhanced || enhancementJustCompleted);

  // Clear validation messages for specific fields
  const getValidationMessage = useCallback(() => {
    if (!id) return 'Please save the article first';
    if (!validation.title) return 'Title needs at least 50 characters';
    if (!validation.enhancedTitle)
      return 'Enhanced English Title needs at least 50 characters';
    if (!validation.enhancedSummary)
      return 'Enhanced English Summary needs at least 50 characters';
    if (!validation.enhancedContent)
      return 'Enhanced English Content needs at least 50 characters';
    return null;
  }, [id, validation]);

  // Translation button logic:
  // For curated articles: Can translate when enhanced fields meet validation (50+ chars each)
  // For generated articles: Must be AI-enhanced first
  const canTranslate =
    articleType === 'curated'
      ? // Curated: Use same validation as enhance button (50+ chars each field)
        !!id && // Must be saved first
        allFieldsValid && // All enhanced fields have 50+ characters
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage)
      : // Generated: Must be AI-enhanced first
        isEnhanced &&
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage);

  // Enhancement button logic: Only show for curated articles
  const canEnhance =
    !!id && // Must be saved first
    articleType === 'curated' &&
    ['curated-draft', 'candidate-article'].includes(workflowStage) && // Allow re-enhancement like re-translation
    allFieldsValid &&
    !isEnhancing &&
    !isTranslating; // Prevent simultaneous operations

  // Button visibility logic following PayloadCMS best practices
  const showEnhanceButton = articleType === 'curated' && !!id; // Only for curated articles
  const showTranslationButton = !!id; // Show for all saved articles

  // Clean debug logging for development (remove in production)
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 DocumentControls state:', {
        articleType,
        workflowStage,
        isEnhanced,
        allFieldsValid,
        showEnhanceButton,
        showTranslationButton,
      });
    }
  }, [
    articleType,
    workflowStage,
    isEnhanced,
    allFieldsValid,
    showEnhanceButton,
    showTranslationButton,
  ]);

  // Translation handler - for both curated and generated articles
  const handleTranslateToGerman = useCallback(async () => {
    if (!canTranslate) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Cannot translate article at this time');
      return;
    }

    setIsTranslating(true);

    try {
      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId: id,
          // No form data - API will work with saved database content only
        }),
      });

      const result = await response.json();

      if (result.success && result.translatedContent) {
        // Show success feedback to user
        toast.success('German translation completed successfully!', {
          description: hasGermanTranslation
            ? 'Article has been re-translated to German.'
            : 'Article has been translated to German.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns
        // The API response contains the translated content
        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanTitle',
          value: result.translatedContent.germanTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanContent',
          value: result.translatedContent.germanContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanSummary',
          value: result.translatedContent.germanSummary,
        });

        // Update additional fields if they exist in the response
        if (result.translatedContent.germanKeyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeyInsights',
            value: result.translatedContent.germanKeyInsights,
          });
        }

        if (result.translatedContent.germanKeywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeywords',
            value: result.translatedContent.germanKeywords,
          });
        }

        dispatchFields({
          type: 'UPDATE',
          path: 'hasGermanTranslation',
          value: true,
        });

        // Update workflow stage to 'translated'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'translated',
        });

        // Set immediate visual feedback
        setTranslationJustCompleted(true);

        // Force form re-render using PayloadCMS best practices
        setTimeout(() => {
          router.refresh();
        }, 1500);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);
      } else {
        toast.error('Translation failed', {
          description:
            result.error || 'An unknown error occurred during translation',
        });
      }
    } catch (error) {
      toast.error('Translation failed', {
        description: 'Failed to communicate with the translation service',
      });
    } finally {
      setIsTranslating(false);
    }
  }, [
    canTranslate,
    id,
    hasGermanTranslation,
    getValidationMessage,
    dispatchFields,
    toast,
    router,
  ]);

  // Enhancement handler - for curated articles
  const handleEnhance = useCallback(async () => {
    if (!canEnhance) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Cannot enhance article at this time');
      return;
    }

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId: id }),
      });

      const result = await response.json();

      if (result.success && result.enhancedContent) {
        // Show success feedback to user (same as translation)
        toast.success('Content enhanced successfully!', {
          description: isEnhanced
            ? 'Article has been re-enhanced.'
            : 'Article has been enhanced with AI.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns (same as translation)
        // The API response contains the enhanced content
        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedTitle',
          value: result.enhancedContent.enhancedTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedContent',
          value: result.enhancedContent.enhancedContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedSummary',
          value: result.enhancedContent.summary,
        });

        // Update additional fields if they exist in the response
        if (result.enhancedContent.keyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'englishTab.enhancedKeyInsights',
            value: result.enhancedContent.keyInsights,
          });
        }

        if (result.enhancedContent.keywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'englishTab.keywords',
            value: result.enhancedContent.keywords,
          });
        }

        // Set enhancement flag (critical for button state changes)
        dispatchFields({
          type: 'UPDATE',
          path: 'hasBeenEnhanced',
          value: true,
        });

        // Update workflow stage to 'candidate-article'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'candidate-article',
        });

        // Set immediate visual feedback (same as translation)
        setEnhancementJustCompleted(true);

        // Force form re-render to load updated data from database
        setTimeout(() => {
          router.refresh();
        }, 1000);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setEnhancementJustCompleted(false);
        }, 2000);
      } else {
        toast.error('Enhancement failed', {
          description: result.error || 'Unknown error during enhancement',
        });
      }
    } catch (error) {
      toast.error('Enhancement failed', {
        description: 'Failed to communicate with enhancement service',
      });
    } finally {
      setIsEnhancing(false);
    }
  }, [
    canEnhance,
    id,
    getValidationMessage,
    dispatchFields,
    toast,
    router,
    isEnhanced,
  ]);

  // Show for both generated and curated articles with appropriate workflow stages
  const validArticleTypes = ['generated', 'curated'];
  const validWorkflowStages = [
    'curated-draft', // New stage for curated articles
    'candidate-article',
    'translated',
    'ready-for-review',
    'published',
  ];

  if (
    !validArticleTypes.includes(articleType) ||
    !validWorkflowStages.includes(workflowStage)
  ) {
    return null;
  }

  // Button disabled states using proper business logic
  const isTranslationDisabled =
    !canTranslate ||
    isTranslating ||
    isEnhancing ||
    translationJustCompleted ||
    enhancementJustCompleted;

  const isEnhancementDisabled =
    !canEnhance ||
    isEnhancing ||
    isTranslating ||
    enhancementJustCompleted ||
    translationJustCompleted;

  // Enhancement button helper functions
  const getEnhanceButtonText = () => {
    if (isEnhancing) return 'Enhancing Content...';
    if (enhancementJustCompleted) return 'Enhancement Complete! Refreshing...';
    if (isEnhanced) return 'Re-enhance Content';
    return 'Enhance Content';
  };

  const getEnhanceButtonColor = () => {
    if (isEnhancing) return '#6B7280'; // Gray for loading
    if (enhancementJustCompleted) return '#10B981'; // Bright green for success
    if (isEnhanced) return '#8B5CF6'; // Purple for re-enhancement
    return '#3B82F6'; // Blue for first enhancement
  };

  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';
    if (hasGermanTranslation) return 'Re-Translate to German';
    return 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  return (
    <div
      className="flex gap-4 items-center mb-4"
      style={{
        marginBottom: 'var(--base, 1rem)',
        gap: 'var(--base-half, 0.5rem)', // Ensure proper spacing between buttons
      }}
    >
      {/* Enhancement Button - Only show for curated articles in draft stage */}
      {showEnhanceButton && (
        <button
          onClick={handleEnhance}
          disabled={isEnhancementDisabled}
          title={
            !id
              ? 'Please save the article first before enhancement'
              : !allFieldsValid
                ? getValidationMessage() ||
                  'Please complete all required fields (50+ characters each)'
                : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getEnhanceButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isEnhancementDisabled ? 'not-allowed' : 'pointer',
            opacity: isEnhancementDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isEnhancing && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {enhancementJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getEnhanceButtonText()}</span>
        </button>
      )}

      {/* Translation Button - Only show when conditions are met */}
      {showTranslationButton && (
        <button
          onClick={handleTranslateToGerman}
          disabled={isTranslationDisabled}
          title={
            !id
              ? 'Please save the article first before translation'
              : !allFieldsValid
                ? getValidationMessage() ||
                  'Please complete all required fields (50+ characters each)'
                : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getTranslationButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
            opacity: isTranslationDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isTranslating && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {translationJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getTranslationButtonText()}</span>
        </button>
      )}

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default ArticleDocumentControls;
